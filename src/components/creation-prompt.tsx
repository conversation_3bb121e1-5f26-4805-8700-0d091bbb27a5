import { FileText, Film, Image, Type } from 'lucide-react'
import { useMemo } from 'react'
import { Badge } from '@/components/ui/badge'
import { getJobTypeClass } from '@/lib/utils'
import { JobType } from '@/stores/creation'

type CreationPromptProps = {
  prompt: string
  setPrompt: (prompt: string) => void
  type?: JobType
}
export default function CreationPrompt({
  prompt,
  setPrompt,
  type = JobType.DEFAULT,
}: CreationPromptProps) {
  const typeText = useMemo(() => {
    switch (type) {
      case JobType.VARIATION:
        return '变化'
      case JobType.UPSCALE:
        return '高清'
      case JobType.REMIX:
        return '重塑'
      case JobType.PAN:
        return '延展'
      case JobType.OUTPAINT:
        return '缩放'
      case JobType.REMOVE_BACKGROUND:
        return '去除背景'
      case JobType.VIDEO_GENERATION:
        return '视频'
      case JobType.VIDEO_EXTEND:
        return '延长'
      case JobType.VIDEO_UPSCALE:
        return '高清'
      case JobType.ARTICLE_GENERATION:
        return '文章'
      default:
        return '图片'
    }
  }, [type])
  const generationType = useMemo(() => {
    return getJobTypeClass(type)
  }, [type])
  const titleText = useMemo(() => {
    if (generationType === 'image') {
      return '图片'
    }
    if (generationType === 'video') {
      return '视频'
    }
    return '文章'
  }, [generationType])

  return (
    <div
      className="p-1 w-full rounded-md relative overflow-hidden bg-transparent hover:bg-background/80 group-hover:bg-background/60 transition-colors duration-300 group xl:min-h-11"
      onClick={() => setPrompt(prompt)}
    >
      <div
        className="break-word empty:hidden shrink-0 text-sm relative first-letter:uppercase text-foreground/80 group-hover:text-foreground line-clamp-[99] cursor-pointer"
        style={{
          minHeight: 20,
          WebkitLineClamp: 4,
          textWrap: 'pretty',
        }}
      >
        <Badge
          variant="secondary"
          title={titleText}
          className="bg-blue-500 text-white dark:bg-blue-600 font-normal mr-1 px-1.5 rounded"
        >
          {generationType === 'image' && (
            <Image className="size-4 mr-0.5 inline-block vertical-middle" />
          )}
          {generationType === 'video' && (
            <Film className="size-4 mr-0.5 inline-block vertical-middle" />
          )}
          {generationType === 'article' && (
            <FileText className="size-4 mr-0.5 inline-block vertical-middle" />
          )}
          {typeText}
        </Badge>
        {prompt}
      </div>
      <div className="absolute bottom-0 right-0 p-1 pl-6 bg-gradient-to-r from-muted/0 from-0% via-muted via-15% to-muted/100 to-100% text-muted-foreground text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
        <Type className="size-4 mr-0.5 inline-block vertical-middle -mt-0.5" />
        使用提示词
      </div>
    </div>
  )
}
