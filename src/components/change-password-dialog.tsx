import { zodResolver } from '@hookform/resolvers/zod'
import { X } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { useCommonStore } from '@/stores/common'
import { Button } from './ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from './ui/form'
import { Input } from './ui/input'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from './ui/dialog'

const formSchema = z.object({
  oldPassword: z.string().min(6, {
    message: '密码至少 6 个字符',
  }),
  newPassword: z
    .string()
    .min(6, {
      message: '密码至少 6 个字符',
    })
    .refine(
      value => value !== formSchema.oldPassword,
      '新密码不能与旧密码相同'
    ),
  confirmNewPassword: z
    .string()
    .min(6, {
      message: '密码至少 6 个字符',
    })
    .refine(
      value => value === formSchema.newPassword,
      '确认密码与新密码不一致'
    ),
})

type ChangePasswordDialogProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
}
export default function ChangePasswordDialog({
  open = false,
  onOpenChange,
}: ChangePasswordDialogProps) {
  const { changePassword } = useCommonStore()
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      oldPassword: '',
      newPassword: '',
      confirmNewPassword: '',
    },
  })
  const onSubmit = form.handleSubmit(async data => {
    await changePassword(data.oldPassword, data.newPassword)
    onOpenChange(false)
  })

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="">
        <DialogHeader>
          <DialogTitle>修改密码</DialogTitle>
        </DialogHeader>
        <DialogClose asChild>
          <Button
            variant="outline"
            size="icon"
            className="absolute top-2 right-2 z-50"
            onClick={() => onOpenChange(false)}
          >
            <X className="size-4" />
          </Button>
        </DialogClose>
        <Form {...form}>
          <form onSubmit={onSubmit} className="space-y-8">
            <FormField
              control={form.control}
              name="oldPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>旧密码</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>新密码</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="confirmNewPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>确认新密码</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit">修改密码</Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
